<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['icon', 'title', 'weight', 'price', 'popular' => false, 'bookingUrl' => '#']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['icon', 'title', 'weight', 'price', 'popular' => false, 'bookingUrl' => '#']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="price-card text-center p-4 sm:p-6 lg:p-8 rounded-xl sm:rounded-2xl mx-auto max-w-sm <?php echo e($popular ? 'bg-white text-gray-900 shadow-2xl relative sm:scale-105' : 'glass-card text-white shadow-lg'); ?>">
    <?php if($popular): ?>
        <div class="absolute top-0 -translate-y-1/2 left-1/2 -translate-x-1/2 bg-orange-600 text-white font-bold text-xs px-3 py-1 rounded-full uppercase tracking-wider">Most Popular</div>
    <?php endif; ?>

    <i class="<?php echo e($icon); ?> text-3xl sm:text-4xl lg:text-5xl mb-4 sm:mb-6 <?php echo e($popular ? 'text-orange-600' : 'text-white opacity-70'); ?>"></i>
    <h3 class="text-xl sm:text-2xl font-bold mb-1"><?php echo e($title); ?></h3>
    <p class="<?php echo e($popular ? 'text-gray-500' : 'opacity-70'); ?> text-xs sm:text-sm mb-4"><?php echo e($weight); ?></p>
    <p class="text-3xl sm:text-4xl lg:text-5xl font-extrabold mb-4 sm:mb-6 <?php echo e($popular ? 'text-orange-600' : ''); ?>"><?php echo e($price); ?></p>

    <a href="<?php echo e($bookingUrl); ?>"
       class="<?php echo e($popular ? 'bg-orange-600 text-white font-bold py-3 px-6 rounded-lg hover:bg-orange-700' : 'bg-white/20 hover:bg-white/30 font-semibold py-3 px-6 rounded-lg'); ?> transition-colors w-full block text-sm sm:text-base">
        Book Now
    </a>
</div>
<?php /**PATH C:\xampp\htdocs\ttajetcom\resources\views/components/price-card.blade.php ENDPATH**/ ?>