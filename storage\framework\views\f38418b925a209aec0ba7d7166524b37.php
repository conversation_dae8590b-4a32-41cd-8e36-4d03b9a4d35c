<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['action' => '#', 'method' => 'GET']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['action' => '#', 'method' => 'GET']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<?php if (isset($component)) { $__componentOriginalf225215015f2c1140bc03ba841300625 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf225215015f2c1140bc03ba841300625 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.glass-card','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('glass-card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <h2 class="text-xl sm:text-2xl font-bold mb-4 sm:mb-6">Quick Booking</h2>
    <form id="quick-booking-form" class="space-y-3 sm:space-y-4" action="<?php echo e($action); ?>" method="<?php echo e($method); ?>">
        <?php if($method !== 'GET'): ?>
            <?php echo csrf_field(); ?>
        <?php endif; ?>

        <div class="relative">
            <i class="fas fa-map-marker-alt absolute left-3 sm:left-4 top-1/2 -translate-y-1/2 text-gray-300 text-sm"></i>
            <input type="text" name="pickup_location" placeholder="Enter pickup location"
                   class="w-full p-3 sm:p-3 pl-10 sm:pl-12 rounded-lg bg-white/10 border border-white/20 focus:outline-none focus:ring-2 focus:ring-orange-500 text-sm sm:text-base"
                   style="font-size: 16px;">
        </div>

        <div class="relative">
            <i class="fas fa-map-marker-alt absolute left-3 sm:left-4 top-1/2 -translate-y-1/2 text-gray-300 text-sm"></i>
            <input type="text" name="delivery_location" placeholder="Enter delivery location"
                   class="w-full p-3 sm:p-3 pl-10 sm:pl-12 rounded-lg bg-white/10 border border-white/20 focus:outline-none focus:ring-2 focus:ring-orange-500 text-sm sm:text-base"
                   style="font-size: 16px;">
        </div>

        <div class="relative">
            <i class="fas fa-box absolute left-3 sm:left-4 top-1/2 -translate-y-1/2 text-gray-300 text-sm"></i>
            <select name="package_type"
                    class="w-full p-3 sm:p-3 pl-10 sm:pl-12 pr-10 rounded-lg bg-white/10 border border-white/20 focus:outline-none focus:ring-2 focus:ring-orange-500 appearance-none text-sm sm:text-base"
                    style="font-size: 16px;">
                <option class="bg-gray-800">Select package type</option>
                <option class="bg-gray-800" value="document">Document</option>
                <option class="bg-gray-800" value="small_box">Small Box</option>
                <option class="bg-gray-800" value="medium_box">Medium Box</option>
                <option class="bg-gray-800" value="large_box">Large Box</option>
            </select>
            <i class="fas fa-chevron-down absolute right-3 sm:right-4 top-1/2 -translate-y-1/2 text-gray-300 text-sm"></i>
        </div>

        <button type="submit" class="w-full bg-orange-600 text-white font-bold py-3 sm:py-3 rounded-lg hover:bg-orange-700 transition-colors text-sm sm:text-base">
            Get Started
        </button>
    </form>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf225215015f2c1140bc03ba841300625)): ?>
<?php $attributes = $__attributesOriginalf225215015f2c1140bc03ba841300625; ?>
<?php unset($__attributesOriginalf225215015f2c1140bc03ba841300625); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf225215015f2c1140bc03ba841300625)): ?>
<?php $component = $__componentOriginalf225215015f2c1140bc03ba841300625; ?>
<?php unset($__componentOriginalf225215015f2c1140bc03ba841300625); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\ttajetcom\resources\views/components/quick-booking-form.blade.php ENDPATH**/ ?>