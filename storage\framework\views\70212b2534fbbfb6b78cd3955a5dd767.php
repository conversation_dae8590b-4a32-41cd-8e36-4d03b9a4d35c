<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['title', 'subtitle', 'primaryButtonText' => 'Get Started', 'primaryButtonUrl' => '#', 'secondaryButtonText' => 'Learn More', 'secondaryButtonUrl' => '#']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['title', 'subtitle', 'primaryButtonText' => 'Get Started', 'primaryButtonUrl' => '#', 'secondaryButtonText' => 'Learn More', 'secondaryButtonUrl' => '#']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<section class="hero-section relative text-white min-h-screen flex items-center">
    <div class="absolute inset-0 bg-black/85"></div>
    <div class="container mx-auto px-4 sm:px-6 relative z-10">
        <div class="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            <div class="text-content order-2 lg:order-1">
                <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-extrabold leading-tight mb-4 sm:mb-6">
                    <?php echo e($title); ?>

                </h1>
                <p class="text-base sm:text-lg text-gray-300 max-w-xl mb-6 sm:mb-8 lg:mb-10">
                    <?php echo e($subtitle); ?>

                </p>
                <div class="flex flex-col sm:flex-row gap-3 sm:gap-4">
                    <a href="<?php echo e($primaryButtonUrl); ?>"
                       class="bg-orange-600 px-6 sm:px-8 py-3 sm:py-4 rounded-lg text-base sm:text-lg font-semibold hover:bg-orange-700 transition-colors text-center w-full sm:w-auto">
                        <?php echo e($primaryButtonText); ?>

                    </a>
                    <a href="<?php echo e($secondaryButtonUrl); ?>"
                       class="bg-white/10 border border-white/20 px-6 sm:px-8 py-3 sm:py-4 rounded-lg text-base sm:text-lg font-semibold hover:bg-white/20 transition-colors text-center w-full sm:w-auto">
                        <?php echo e($secondaryButtonText); ?>

                    </a>
                </div>
            </div>

            <?php if(isset($form)): ?>
                <div class="booking-form order-1 lg:order-2 w-full">
                    <?php echo e($form); ?>

                </div>
            <?php endif; ?>
        </div>
    </div>
</section>
<?php /**PATH C:\xampp\htdocs\ttajetcom\resources\views/components/hero-section.blade.php ENDPATH**/ ?>