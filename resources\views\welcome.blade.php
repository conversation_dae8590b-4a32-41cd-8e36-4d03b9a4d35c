@extends('layouts.app')

@section('title', 'TTAJet - Fast and Reliable Courier Services')

@section('content')

@php
    // Get settings for pricing display
    $pricingSettings = \App\Models\Setting::getGroup('pricing');

    // Set default pricing if settings don't exist
    $baseCosts = [
        'document' => $pricingSettings['base_cost_document'] ?? 15.00,
        'small' => $pricingSettings['base_cost_small'] ?? 20.00,
        'medium' => $pricingSettings['base_cost_medium'] ?? 35.00,
        'large' => $pricingSettings['base_cost_large'] ?? 50.00,
    ];
@endphp

<div id="app">

    <!-- Hero Section -->
    <x-hero-section
        title="Fast & Reliable Courier Services"
        subtitle="Book a courier with TTA Jet for quick, secure, and professional delivery service across Accra."
        primary-button-text="Join Now"
        primary-button-url="{{ route('register') }}"
        secondary-button-text="About Us"
        secondary-button-url="#about-preview">

        <x-slot name="form">
            @guest
                <x-quick-booking-form action="{{ route('booking.create') }}" />
            @else
                <x-glass-card>
                    <h2 class="text-2xl font-bold mb-6">Welcome back!</h2>
                    <div class="text-center"> 
                        <p class="text-gray-300 mb-4">Hello, {{ auth()->user()->name }}!</p>
                        <a href="{{ route('booking.create') }}"
                           class="inline-block w-full bg-orange-600 text-white font-bold py-3 rounded-lg hover:bg-orange-700 transition-colors text-center mb-3">
                            Create New Booking
                        </a>
                        <a href="{{ route('customer.dashboard') }}"
                           class="inline-block w-full bg-white/10 border border-white/20 text-white font-semibold py-3 rounded-lg hover:bg-white/20 transition-colors text-center">
                            View Dashboard
                        </a>
                    </div>
                </x-glass-card>
            @endguest
        </x-slot>
    </x-hero-section>

    <!-- About Preview Section -->
    <section id="about-preview" class="py-12 sm:py-16 lg:py-20 xl:py-28 bg-white" style="margin-top: 5vh;">
        <div class="container mx-auto px-4 sm:px-6">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4 sm:mb-6">Your Trusted Partner in Delivery</h2>
                <p class="text-base sm:text-lg text-gray-600 leading-relaxed mb-6 sm:mb-8">TTA Jet is your trusted partner for fast and reliable courier delivery within Accra. We're committed to providing exceptional service with every package we handle. Our mission is to bridge distances, connecting people and businesses with efficiency and care.</p>
                <a href="#about" class="inline-block font-semibold brand-orange-text hover:underline text-base sm:text-lg">Learn More About Us <i class="fas fa-arrow-right ml-1 text-sm"></i></a>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-12 sm:py-16 lg:py-20 bg-gray-50">
        <div class="container mx-auto px-4 sm:px-6">
            <h2 class="text-2xl sm:text-3xl lg:text-4xl font-bold mb-8 sm:mb-12 lg:mb-16 text-center text-gray-900">Our Core Services</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 max-w-6xl mx-auto">
                <x-service-card
                    icon="fas fa-shipping-fast"
                    title="Same-Day Delivery"
                    description="Get your packages delivered on the same day with unparalleled speed and care." />

                <x-service-card
                    icon="fas fa-box-open"
                    title="Small Packages"
                    description="We handle all your small parcels with maximum efficiency and security." />

                <x-service-card
                    icon="fas fa-store"
                    title="Business Solutions"
                    description="Tailored courier services to meet the demands of your small business." />
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="py-12 sm:py-16 lg:py-20 xl:py-28 flex items-center justify-center min-h-screen">
        <div class="container mx-auto px-4 sm:px-6">
            <h2 class="text-2xl sm:text-3xl lg:text-4xl font-bold mb-8 sm:mb-12 lg:mb-16 text-center text-white">Simple, Transparent Pricing</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 max-w-6xl mx-auto">
                <x-price-card
                    icon="fas fa-file-alt"
                    title="Document"
                    weight="< 0.5 kg"
                    price="{{ \App\Models\Setting::formatCurrency($baseCosts['document']) }}"
                    booking-url="{{ route('booking.create') }}" />

                <x-price-card
                    icon="fas fa-box"
                    title="Small Box"
                    weight="0.5 - 2 kg"
                    price="{{ \App\Models\Setting::formatCurrency($baseCosts['small']) }}"
                    :popular="true"
                    booking-url="{{ route('booking.create') }}" />

                <x-price-card
                    icon="fas fa-box-open"
                    title="Medium Box"
                    weight="2 - 5 kg"
                    price="{{ \App\Models\Setting::formatCurrency($baseCosts['medium']) }}"
                    booking-url="{{ route('booking.create') }}" />

                <x-price-card
                    icon="fas fa-boxes"
                    title="Large Box"
                    weight="> 5 kg"
                    price="{{ \App\Models\Setting::formatCurrency($baseCosts['large']) }}"
                    booking-url="{{ route('booking.create') }}" />
            </div>
        </div>
    </section>

    <!-- Reviews Section -->
    <section id="reviews" class="bg-white py-12 sm:py-16 lg:py-20 xl:py-28">
        <div class="container mx-auto px-4 sm:px-6">
            <div class="grid lg:grid-cols-2 gap-8 lg:gap-16 items-center max-w-6xl mx-auto">
                <div class="review-text-content text-center lg:text-left">
                    <h2 class="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4 sm:mb-6">Discover What Our Clients Say About Us!</h2>
                    <p class="text-base sm:text-lg text-gray-600 leading-relaxed">Our platform involves a comprehensive approach focused on attracting new customers while ensuring the loyalty and satisfaction of existing ones.</p>
                </div>
                <div class="relative flex items-center justify-center min-h-[300px] sm:min-h-[350px]">
                    <div class="flex items-center justify-center w-full">
                        <div id="review-card" class="bg-white p-4 sm:p-6 lg:p-8 rounded-xl sm:rounded-2xl shadow-xl sm:shadow-2xl border w-full max-w-sm sm:max-w-md">
                            <p id="reviewer-quote" class="text-sm sm:text-base lg:text-lg text-gray-700 leading-relaxed">"As a busy professional, managing shipments can be challenging, but TTAJet simplifies this task by providing me with the tools to stay on top of my logistics."</p>
                            <div class="mt-3 sm:mt-4 border-t pt-3 sm:pt-4">
                                <p id="reviewer-name" class="font-bold text-gray-900 text-sm sm:text-base">Ama Serwaa</p>
                                <p id="reviewer-role" class="text-xs sm:text-sm text-gray-500">CEO, Serwaa Enterprises</p>
                            </div>
                        </div>
                    </div>
                    <button id="prev-review" class="absolute top-1/2 -translate-y-1/2 -left-2 sm:-left-4 lg:-left-6 w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-orange-600 text-white rounded-full flex items-center justify-center shadow-lg hover:bg-orange-700 transition-colors">
                        <i class="fas fa-chevron-left text-xs sm:text-sm"></i>
                    </button>
                    <button id="next-review" class="absolute top-1/2 -translate-y-1/2 -right-2 sm:-right-4 lg:-right-6 w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-orange-600 text-white rounded-full flex items-center justify-center shadow-lg hover:bg-orange-700 transition-colors">
                        <i class="fas fa-chevron-right text-xs sm:text-sm"></i>
                    </button>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection

@push('scripts')
<script>
    // Additional page-specific JavaScript can be added here
    // The main animations and functionality are handled by the unified app.js
</script>
@endpush
