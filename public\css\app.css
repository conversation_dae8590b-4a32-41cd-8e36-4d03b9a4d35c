/* TTAJet Courier Service - Unified Styles */

/* Base Styles */
body {
    font-family: 'Poppins', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #F9FAFB; /* Light gray base background */
}

/* Brand Colors - Restricted to 3-color palette */
.brand-orange {
    background-color: #F97316;
}

.brand-orange-text {
    color: #F97316;
}

.brand-orange-border {
    border-color: #F97316;
}

/* Enhanced Glassmorphism Effect */
.glass-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Hero Section */
.hero-section {
    background-image: url('https://img.freepik.com/free-vector/worldwide-global-map-outline-black-background_1017-46153.jpg');
    background-size: cover;
    background-position: center;
}

/* Pricing Section */
#pricing {
    background-image: linear-gradient(to top right, #181818, #000000);
}

/* Service Cards */
.service-card {
    transition: all 0.3s ease;
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Price Cards */
.price-card {
    transition: all 0.3s ease;
}

.price-card:hover {
    transform: translateY(-4px);
}

/* Form Styles */
.form-input-icon {
    position: absolute;
    left: 1.25rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9CA3AF;
    pointer-events: none;
}

.form-input {
    padding-left: 3.5rem !important;
}

/* Loading Spinner */
.spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #F97316;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #F97316;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #ea580c;
}

/* Navigation Styles */
header {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Button Styles */
.btn-primary {
    background-color: #F97316;
    color: white;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: #ea580c;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Toast Notifications */
.toast-notification {
    min-width: 300px;
    max-width: 500px;
}

/* Mobile Menu Styles */
.mobile-menu-button {
    display: block;
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease-in-out;
    z-index: 60;
    position: relative;
}

.mobile-menu-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.mobile-menu-button:focus {
    outline: 2px solid #F97316;
    outline-offset: 2px;
}

/* Mobile Navigation Menu */
.mobile-nav-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 55;
}

/* Ensure Alpine.js x-show works properly */
[x-cloak] {
    display: none !important;
}

/* Mobile menu - hidden by default */
.mobile-menu {
    display: none !important;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.2s ease-in-out;
}

/* Mobile menu - visible state */
.mobile-menu.show {
    display: block !important;
    opacity: 1;
    transform: translateY(0);
}

/* Force mobile menu to be hidden initially */
[x-show="mobileMenuOpen"] {
    display: none !important;
}

/* Override when Alpine.js shows the menu */
[x-show="mobileMenuOpen"][style*="display: block"] {
    display: block !important;
}

/* Mobile menu animation */
.mobile-menu-slide-down {
    animation: slideDown 0.2s ease-out;
}

.mobile-menu-slide-up {
    animation: slideUp 0.2s ease-in;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
    }
}

/* Ensure mobile menu button is visible on all mobile devices */
@media (max-width: 767px) {
    .md\:hidden {
        display: block !important;
    }

    .mobile-menu-button {
        min-width: 44px;
        min-height: 44px;
        display: flex !important;
        align-items: center;
        justify-content: center;
    }

    /* Force mobile menu to be properly positioned */
    header [x-show="mobileMenuOpen"] {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        width: 100%;
        z-index: 999;
        display: none !important; /* Force hidden by default */
    }

    /* Ensure mobile menu is hidden by default */
    header [x-show="mobileMenuOpen"]:not(.show) {
        display: none !important;
    }

    /* Ensure mobile menu is visible when shown */
    header [x-show="mobileMenuOpen"].show {
        display: block !important;
    }

    /* Override Alpine.js inline styles when needed */
    header [x-show="mobileMenuOpen"][style*="display: block"].show {
        display: block !important;
    }
}

/* Mobile-First Responsive Design */

/* Mobile Base Styles (320px and up) */
@media (max-width: 480px) {
    /* Typography - Mobile First */
    .hero-section h1 {
        font-size: 2.5rem !important;
        line-height: 1.1;
        margin-bottom: 1rem;
    }

    .hero-section p {
        font-size: 1rem;
        line-height: 1.5;
        margin-bottom: 1.5rem;
    }

    /* Container padding for mobile */
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    /* Section spacing using rule of thirds */
    section {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }

    /* Hero section mobile adjustments */
    .hero-section {
        min-height: 100vh;
        padding-top: 6rem; /* Account for fixed header */
    }

    .hero-section .grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    /* Button improvements for mobile */
    .hero-section .flex {
        flex-direction: column;
        gap: 1rem;
    }

    .hero-section a {
        padding: 1rem 2rem;
        font-size: 1rem;
        text-align: center;
        width: 100%;
    }

    /* Service cards mobile layout */
    .service-card {
        margin-bottom: 1.5rem;
        padding: 1.5rem;
    }

    .service-card .w-20 {
        width: 4rem;
        height: 4rem;
        margin-bottom: 1rem;
    }

    .service-card i {
        font-size: 2rem;
    }

    /* Price cards mobile layout */
    .price-card {
        margin-bottom: 1.5rem;
        padding: 1.5rem;
    }

    .price-card i {
        font-size: 2.5rem;
    }

    .price-card .text-5xl {
        font-size: 2.5rem;
    }

    /* Form elements mobile optimization */
    input, select, textarea {
        font-size: 16px; /* Prevent zoom on iOS */
        padding: 0.875rem;
    }

    /* Glass card mobile adjustments */
    .glass-card {
        padding: 1.5rem;
        margin: 1rem;
    }

    /* Navigation logo mobile size */
    header img {
        height: 50px !important;
    }

    /* Mobile menu improvements */
    .mobile-menu {
        margin-top: 0;
        border-radius: 0;
    }

    .mobile-menu .px-6 {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    /* Review section mobile */
    #review-card {
        padding: 1.5rem;
        margin: 0 1rem;
    }

    #prev-review, #next-review {
        width: 2.5rem;
        height: 2.5rem;
        left: -0.5rem;
        right: -0.5rem;
    }
}

/* Small Mobile (up to 375px) */
@media (max-width: 375px) {
    .hero-section h1 {
        font-size: 2rem !important;
    }

    .container {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .service-card, .price-card {
        padding: 1rem;
    }

    .glass-card {
        padding: 1rem;
        margin: 0.5rem;
    }
}

/* Tablet Portrait (481px to 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    .hero-section h1 {
        font-size: 3.5rem;
    }

    .hero-section .grid {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .service-card, .price-card {
        margin-bottom: 2rem;
    }

    /* Two column layout for service cards on tablet */
    .grid.sm\\:grid-cols-2 {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
}

/* Tablet Landscape and Small Desktop (769px to 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    .hero-section h1 {
        font-size: 4rem;
    }

    .container {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Fallback styles to ensure elements are visible - only apply to elements that exist */
.text-content > *,
.booking-form,
.service-card,
.price-card,
#about-preview *,
#reviews * {
    opacity: 1 !important;
    transform: translateY(0) scale(1) !important;
    visibility: visible !important;
}

/* Only hide elements if JavaScript is enabled and GSAP is working - and only on homepage */
body.js-animations-enabled .text-content > *,
body.js-animations-enabled .booking-form,
body.js-animations-enabled .service-card,
body.js-animations-enabled .price-card {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
}

/* Ensure booking form pages are never affected by homepage animations */
body:not(.homepage) .text-content > *,
body:not(.homepage) .booking-form,
body:not(.homepage) .service-card,
body:not(.homepage) .price-card {
    opacity: 1 !important;
    transform: translateY(0) scale(1) !important;
    visibility: visible !important;
}

/* Focus States */
input:focus, select:focus, textarea:focus {
    outline: none;
    box-shadow: 0 0 0 2px #F97316;
    border-color: #F97316;
}

/* Card Shadows */
.card-shadow {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.card-shadow-lg {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Utility Classes */
.text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.backdrop-blur {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Review Section Styles */
#review-card {
    transition: all 0.3s ease;
}

/* Newsletter Section */
.newsletter-section {
    background: linear-gradient(190deg, #2222225d 0%, #00000098 100%);
}

/* Footer Enhancements */
footer {
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
}

/* Social Icons */
.social-icon {
    width: 36px;
    height: 36px;
    background-color: #202020;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.social-icon:hover {
    background-color: #F97316;
    color: white;
    transform: translateY(-2px);
}

/* Mobile Bottom Navigation */
.mobile-bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    padding: 0.5rem 0;
    display: none;
}

.mobile-bottom-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    color: #9CA3AF;
    text-decoration: none;
    transition: all 0.2s ease;
    font-size: 0.75rem;
    min-height: 60px;
}

.mobile-bottom-nav-item:hover,
.mobile-bottom-nav-item.active {
    color: #F97316;
    transform: translateY(-2px);
}

.mobile-bottom-nav-item i {
    font-size: 1.25rem;
    margin-bottom: 0.25rem;
}

.mobile-bottom-nav-item span {
    font-size: 0.625rem;
    font-weight: 500;
}

/* Show mobile bottom nav only on mobile devices */
@media (max-width: 768px) {
    .mobile-bottom-nav {
        display: flex;
    }

    /* Add bottom padding to body to account for bottom nav */
    body {
        padding-bottom: 70px;
    }

    /* Adjust scroll to top button position */
    #scroll-to-top {
        bottom: 80px;
    }
}

/* Mobile-specific layout improvements */
@media (max-width: 768px) {
    /* Ensure main content doesn't overlap with fixed header */
    main {
        margin-top: 80px !important;
    }

    /* Improve section spacing on mobile */
    section {
        margin-bottom: 2rem;
    }

    /* Better mobile grid layouts */
    .grid {
        gap: 1rem;
    }

    .grid.gap-8 {
        gap: 1.5rem;
    }

    .grid.gap-12 {
        gap: 2rem;
    }

    .grid.gap-16 {
        gap: 2.5rem;
    }

    /* Mobile text improvements */
    .text-3xl {
        font-size: 1.75rem;
    }

    .text-4xl {
        font-size: 2rem;
    }

    .text-5xl {
        font-size: 2.5rem;
    }

    .text-7xl {
        font-size: 3rem;
    }

    /* Mobile padding and margin utilities */
    .py-20 {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }

    .py-28 {
        padding-top: 4rem;
        padding-bottom: 4rem;
    }

    .mb-16 {
        margin-bottom: 2rem;
    }

    .mt-10 {
        margin-top: 2rem;
    }

    /* Mobile form improvements */
    .booking-form {
        padding: 1rem;
    }

    .booking-form .grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    /* Mobile card improvements */
    .rounded-2xl {
        border-radius: 1rem;
    }

    .shadow-xl {
        box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }
}

/* Landscape mobile orientation */
@media (max-width: 768px) and (orientation: landscape) {
    .hero-section {
        min-height: 100vh;
        padding-top: auto;
    }

    .hero-section h1 {
        font-size: 2rem !important;
    }

    .hero-section .grid {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        align-items: center;
    }
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
    /* Increase touch targets */
    button, a, input, select, textarea {
        min-height: 44px;
        min-width: 44px;
    }

    /* Remove hover effects on touch devices */
    .service-card:hover,
    .price-card:hover {
        transform: none;
    }

    /* Better touch feedback */
    button:active, a:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }
}

/* Additional mobile fixes */
@media (max-width: 768px) {
    /* Ensure no horizontal scroll */
    body {
        overflow-x: hidden;
    }

    /* Fix any potential layout issues */
    * {
        max-width: 100%;
    }

    /* Improve mobile navigation */
    .mobile-menu {
        max-height: calc(100vh - 80px);
        overflow-y: auto;
    }

    /* Better mobile form styling */
    input[type="text"],
    input[type="email"],
    input[type="tel"],
    input[type="password"],
    select,
    textarea {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        border-radius: 8px;
    }

    /* Prevent zoom on input focus for iOS */
    input[type="text"],
    input[type="email"],
    input[type="tel"],
    input[type="password"],
    select,
    textarea {
        font-size: 16px !important;
    }

    /* Mobile-specific spacing fixes */
    .container {
        max-width: 100%;
    }

    /* Ensure images are responsive */
    img {
        max-width: 100%;
        height: auto;
    }

    /* Mobile-friendly tables */
    table {
        font-size: 14px;
    }

    /* Better mobile card spacing */
    .service-card,
    .price-card {
        margin-left: auto;
        margin-right: auto;
    }
}

/* Very small screens (iPhone SE, etc.) */
@media (max-width: 320px) {
    .container {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    .hero-section h1 {
        font-size: 1.75rem !important;
        line-height: 1.2;
    }

    .mobile-bottom-nav-item {
        padding: 0.25rem;
        min-height: 50px;
    }

    .mobile-bottom-nav-item i {
        font-size: 1rem;
    }

    .mobile-bottom-nav-item span {
        font-size: 0.5rem;
    }
}

/* High DPI screens */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* Ensure crisp rendering on retina displays */
    .mobile-bottom-nav {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}
