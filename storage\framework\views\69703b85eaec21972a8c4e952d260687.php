<header class="bg-black text-white fixed top-0 w-full z-50 backdrop-blur-sm" style="margin-top: 0px !important;" x-data="{ mobileMenuOpen: false }" x-cloak>
    <nav class="container mx-auto px-4 sm:px-6 py-3 sm:py-4 flex justify-between items-center">
        <!-- Logo -->
        <a href="<?php echo e(route('home')); ?>" class="text-xl sm:text-2xl font-bold cursor-pointer">
            <img src="<?php echo e(asset('images/logo.jpeg')); ?>" alt="TTAJET" class="h-12 sm:h-16 lg:h-[75px] w-auto object-cover">
        </a>
        
        <!-- Desktop Navigation -->
        <div class="hidden lg:flex items-center space-x-6 xl:space-x-8">
            <?php if(auth()->guard()->guest()): ?>
                <a href="<?php echo e(route('home')); ?>#about" class="text-gray-300 hover:text-white transition-colors text-sm xl:text-base">About</a>
                <a href="<?php echo e(route('home')); ?>#services" class="text-gray-300 hover:text-white transition-colors text-sm xl:text-base">Services</a>
                <a href="<?php echo e(route('booking.create')); ?>" class="text-gray-300 hover:text-white transition-colors text-sm xl:text-base">Book a Delivery</a>
                <a href="<?php echo e(route('tracking')); ?>" class="text-gray-300 hover:text-white transition-colors text-sm xl:text-base">Track Booking</a>
                <a href="<?php echo e(route('login')); ?>" class="text-gray-300 hover:text-white transition-colors text-sm xl:text-base">Login</a>
                <a href="<?php echo e(route('register')); ?>" class="bg-orange-600 px-4 xl:px-5 py-2 xl:py-2.5 rounded-lg text-xs xl:text-sm font-semibold hover:bg-orange-700 transition-colors">Sign Up</a>
            <?php else: ?>
                <?php if(auth()->user()->isCustomer()): ?>
                    <a href="<?php echo e(route('customer.dashboard')); ?>" class="text-gray-300 hover:text-white transition-colors">Dashboard</a>
                    <a href="<?php echo e(route('booking.create')); ?>" class="text-gray-300 hover:text-white transition-colors">Book Delivery</a>
                    <a href="<?php echo e(route('booking.history')); ?>" class="text-gray-300 hover:text-white transition-colors">My Bookings</a>
                    <a href="<?php echo e(route('tracking')); ?>" class="text-gray-300 hover:text-white transition-colors">Track Booking</a>
                <?php elseif(auth()->user()->isAdmin()): ?>
                    <a href="<?php echo e(route('admin.dashboard')); ?>" class="bg-orange-600 px-5 py-2.5 rounded-lg text-sm font-semibold hover:bg-orange-700 transition-colors">Admin Dashboard</a>
                    <a href="<?php echo e(route('admin.bookings.index')); ?>" class="text-gray-300 hover:text-white transition-colors">Manage Bookings</a>
                    <a href="<?php echo e(route('admin.users.index')); ?>" class="text-gray-300 hover:text-white transition-colors">Manage Users</a>
                    <a href="<?php echo e(route('admin.settings')); ?>" class="text-gray-300 hover:text-white transition-colors">Settings</a>

                <?php endif; ?>
                
                <!-- User Dropdown -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center space-x-2 text-gray-300 hover:text-white transition-colors">
                        <i class="fas fa-user-circle text-xl"></i>
                        <span><?php echo e(auth()->user()->name); ?></span>
                        <i class="fas fa-chevron-down text-sm"></i>
                    </button>
                    
                    <div x-show="open" @click.away="open = false" 
                         x-transition:enter="transition ease-out duration-200"
                         x-transition:enter-start="opacity-0 scale-95"
                         x-transition:enter-end="opacity-100 scale-100"
                         x-transition:leave="transition ease-in duration-75"
                         x-transition:leave-start="opacity-100 scale-100"
                         x-transition:leave-end="opacity-0 scale-95"
                         class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                        
                        <a href="<?php echo e(route('profile.edit')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <i class="fas fa-user mr-2"></i>Profile
                        </a>
                        
                        <?php if(auth()->user()->isCustomer()): ?>
                            <a href="<?php echo e(route('customer.notifications')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-bell mr-2"></i>Notifications
                            </a>
                        <?php endif; ?>
                        
                        <div class="border-t border-gray-100"></div>
                        
                        <form method="POST" action="<?php echo e(route('logout')); ?>">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-sign-out-alt mr-2"></i>Sign Out
                            </button>
                        </form>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Mobile Menu Button -->
        <div class="lg:hidden">
            <button @click="mobileMenuOpen = !mobileMenuOpen; console.log('Mobile menu toggled:', mobileMenuOpen)"
                    class="mobile-menu-button text-white focus:outline-none p-2"
                    aria-label="Toggle mobile menu"
                    :aria-expanded="mobileMenuOpen"
                    type="button">
                <i class="fas text-xl sm:text-2xl" :class="{ 'fa-bars': !mobileMenuOpen, 'fa-times': mobileMenuOpen }"></i>
            </button>
        </div>
    </nav>
    
    <!-- Mobile Navigation Menu -->
    <div x-show="mobileMenuOpen"
         x-transition:enter="transition ease-out duration-200"
         x-transition:enter-start="opacity-0 transform -translate-y-1"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         x-transition:leave="transition ease-in duration-150"
         x-transition:leave-start="opacity-100 transform translate-y-0"
         x-transition:leave-end="opacity-0 transform -translate-y-1"
         @click.away="mobileMenuOpen = false"
         class="mobile-menu lg:hidden bg-black/95 backdrop-blur-sm border-t border-gray-800 w-full"
         role="navigation"
         aria-label="Mobile navigation menu"
         style="display: none !important;"
         x-cloak>

        <div class="px-4 sm:px-6 py-4 space-y-3 sm:space-y-4">
            <?php if(auth()->guard()->guest()): ?>
                <a href="<?php echo e(route('home')); ?>#about" class="block hover:text-orange-500 transition-colors py-2 text-sm sm:text-base">About</a>
                <a href="<?php echo e(route('home')); ?>#services" class="block hover:text-orange-500 transition-colors py-2 text-sm sm:text-base">Services</a>
                <a href="<?php echo e(route('tracking')); ?>" class="block hover:text-orange-500 transition-colors py-2 text-sm sm:text-base">Track Package</a>
                <a href="<?php echo e(route('booking.create')); ?>" class="block hover:text-orange-500 transition-colors py-2 text-sm sm:text-base">Book a Delivery</a>
                <a href="<?php echo e(route('login')); ?>" class="block hover:text-orange-500 transition-colors py-2 text-sm sm:text-base">Login</a>
                <a href="<?php echo e(route('register')); ?>" class="block bg-orange-500 px-4 py-3 rounded-lg hover:bg-orange-600 transition-colors text-center text-sm sm:text-base font-semibold">Sign Up</a>
            <?php else: ?>
                <?php if(auth()->user()->isCustomer()): ?>
                    <a href="<?php echo e(route('customer.dashboard')); ?>" class="block hover:text-orange-500 transition-colors">Dashboard</a>
                    <a href="<?php echo e(route('booking.create')); ?>" class="block hover:text-orange-500 transition-colors">Book Delivery</a>
                    <a href="<?php echo e(route('booking.history')); ?>" class="block hover:text-orange-500 transition-colors">My Bookings</a>
                    <a href="<?php echo e(route('tracking')); ?>" class="block hover:text-orange-500 transition-colors">Track Booking</a>
                <?php elseif(auth()->user()->isAdmin()): ?>
                    <a href="<?php echo e(route('admin.dashboard')); ?>" class="block hover:text-orange-500 transition-colors">Dashboard</a>
                    <a href="<?php echo e(route('admin.bookings.index')); ?>" class="block hover:text-orange-500 transition-colors">Manage Bookings</a>
                    <a href="<?php echo e(route('admin.users.index')); ?>" class="block hover:text-orange-500 transition-colors">Manage Users</a>
                    <a href="<?php echo e(route('admin.settings')); ?>" class="block hover:text-orange-500 transition-colors">Settings</a>

                <?php endif; ?>
                
                <div class="border-t border-gray-800 pt-4">
                    <a href="<?php echo e(route('profile.edit')); ?>" class="block hover:text-orange-500 transition-colors">Profile</a>
                    <form method="POST" action="<?php echo e(route('logout')); ?>" class="mt-2">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="block w-full text-left hover:text-orange-500 transition-colors">Sign Out</button>
                    </form>
                </div>
            <?php endif; ?>
        </div>
    </div>
</header>

<?php $__env->startPush('scripts'); ?>
<script>
// Enhanced mobile menu functionality with Alpine.js support and fallback
document.addEventListener('DOMContentLoaded', function() {
    console.log('Mobile menu script loaded');

    // Wait for Alpine.js to initialize
    setTimeout(function() {
        const mobileButton = document.querySelector('.mobile-menu-button');
        const mobileMenu = document.querySelector('[x-show="mobileMenuOpen"]');

        if (mobileButton && mobileMenu) {
            console.log('Mobile menu elements found');

            // Ensure initial hidden state
            mobileMenu.style.display = 'none';
            mobileMenu.style.setProperty('display', 'none', 'important');
            mobileMenu.classList.remove('show');

            // Enhanced click handler
            mobileButton.addEventListener('click', function(e) {
                console.log('Mobile menu button clicked');
                e.preventDefault();
                e.stopPropagation();

                const isHidden = !mobileMenu.classList.contains('show');

                if (isHidden) {
                    // Show menu
                    mobileMenu.style.setProperty('display', 'block', 'important');
                    mobileMenu.classList.add('show');
                    mobileButton.querySelector('i').className = 'fas fa-times text-2xl';
                    mobileButton.setAttribute('aria-expanded', 'true');
                    console.log('Mobile menu opened');
                } else {
                    // Hide menu
                    mobileMenu.style.setProperty('display', 'none', 'important');
                    mobileMenu.classList.remove('show');
                    mobileButton.querySelector('i').className = 'fas fa-bars text-2xl';
                    mobileButton.setAttribute('aria-expanded', 'false');
                    console.log('Mobile menu closed');
                }
            });

            // Close menu when clicking outside
            document.addEventListener('click', function(e) {
                if (!mobileButton.contains(e.target) && !mobileMenu.contains(e.target)) {
                    if (mobileMenu.classList.contains('show')) {
                        mobileMenu.style.setProperty('display', 'none', 'important');
                        mobileMenu.classList.remove('show');
                        mobileButton.querySelector('i').className = 'fas fa-bars text-2xl';
                        mobileButton.setAttribute('aria-expanded', 'false');
                        console.log('Mobile menu closed (click outside)');
                    }
                }
            });

            // Close menu when clicking on menu links
            const menuLinks = mobileMenu.querySelectorAll('a');
            menuLinks.forEach(link => {
                link.addEventListener('click', function() {
                    setTimeout(() => {
                        mobileMenu.style.setProperty('display', 'none', 'important');
                        mobileMenu.classList.remove('show');
                        mobileButton.querySelector('i').className = 'fas fa-bars text-2xl';
                        mobileButton.setAttribute('aria-expanded', 'false');
                        console.log('Mobile menu closed (link clicked)');
                    }, 100);
                });
            });

        } else {
            console.log('Mobile menu elements not found');
        }
    }, 100);
});
</script>
<?php $__env->stopPush(); ?>


<?php /**PATH C:\xampp\htdocs\ttajetcom\resources\views/partials/navigation.blade.php ENDPATH**/ ?>